/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #000000;
  background-color: #ffffff;
  overflow-x: hidden;
  overflow-y: auto;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.25;
  margin-bottom: 0.5rem;
}

h1 {
  font-size: 2rem;
}
h2 {
  font-size: 1.5rem;
}
h3 {
  font-size: 1.25rem;
}
h4 {
  font-size: 1.125rem;
}
h5 {
  font-size: 1rem;
}
h6 {
  font-size: 0.875rem;
}

p {
  margin-bottom: 1rem;
}

/* Links */
a {
  color: #e6b120;
  text-decoration: none;
  transition: color 0.2s;
}

a:hover {
  color: #ffcd29;
  text-decoration: underline;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  gap: 0.5rem;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #e6b120;
  color: #ffffff;
  border-color: #e6b120;
}

.btn-primary:hover:not(:disabled) {
  background-color: #ffcd29;
  border-color: #ffcd29;
  text-decoration: none;
  color: #000000;
}

.btn-secondary {
  background-color: #000000;
  color: #ffffff;
  border-color: #000000;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #333333;
  border-color: #333333;
  text-decoration: none;
  color: #ffffff;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
  border-color: #ef4444;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
  border-color: #dc2626;
  text-decoration: none;
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: #000000;
  border-color: #e6b120;
}

.btn-outline:hover:not(:disabled) {
  background-color: #ffcd29;
  text-decoration: none;
  color: #000000;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: #000000;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #e6b120;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #000000;
  background-color: #ffffff;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #ffcd29;
  box-shadow: 0 0 0 3px rgba(255, 205, 41, 0.2);
}

.form-control:disabled {
  background-color: #f9fafb;
  color: #6b7280;
}

.form-control.is-invalid {
  border-color: #ef4444;
}

.form-control.is-invalid:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #ef4444;
}

/* Utilities */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.d-none {
  display: none !important;
}
.d-block {
  display: block !important;
}
.d-flex {
  display: flex !important;
}
.d-inline-flex {
  display: inline-flex !important;
}

.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.justify-content-end {
  justify-content: flex-end;
}

.align-items-center {
  align-items: center;
}
.align-items-start {
  align-items: flex-start;
}
.align-items-end {
  align-items: flex-end;
}

.flex-column {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-1 {
  flex: 1;
}

.gap-1 {
  gap: 0.25rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}

/* Additional utility classes */
.text-muted {
  color: #6c757d !important;
}

.text-danger {
  color: #dc3545 !important;
}

.font-weight-500 {
  font-weight: 500 !important;
}

.text-sm {
  font-size: 0.875rem !important;
}

.text-gray-500 {
  color: #6b7280 !important;
}

.main-layout {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  margin-top: 1.25rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  margin-bottom: 120px;
  min-height: 100vh;
}

@media (min-width: 640px) {
  .main-layout {
    margin-top: 1.25rem;
    max-width: 700px;
  }
}

@media (min-width: 768px) {
  .main-layout {
    margin-top: 2rem;
    padding-left: 0;
    padding-right: 0;
  }
}
@media (min-width: 1280px) {
  .main-layout {
    max-width: 1140px;
  }
}

/* Page Transitions */
.page-container {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-container.page-enter {
  opacity: 1;
  transform: translateY(0);
}

.page-container.page-exit {
  opacity: 0;
  transform: translateY(-20px);
}

/* Fade transition for route changes */
#root {
  position: relative;
}

.route-transition {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  z-index: 1;
}

.route-transition.entering {
  animation: routeEnter 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.route-transition.exiting {
  animation: routeExit 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes routeEnter {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes routeExit {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-20px);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Loading states with smooth transitions */
.loading-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Import component styles */
@import "./components/image-upload.css";
@import "./components/image-cropper.css";
@import "./components/searchable-dropdown.css";
@import "./components/price-formatter.css";
@import "./components/qr-generator.css";
