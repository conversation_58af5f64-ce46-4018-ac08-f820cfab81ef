@import url('https://fonts.googleapis.com/css2?family=Istok+Web:ital,wght@0,400;0,700;1,400;1,700&display=swap');
/* Base Styles */
.home-page {
  color: #313131;
}

/* Main Layout */
.home-main-layout {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  margin-top: 1.25rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  margin-bottom: 120px;
}

/* Banner Styles */
.home-banner-container {
  position: relative;
}

.home-banner-item {
  height: 175px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.home-banner-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.home-banner-skeleton {
  width: 100%;
  height: 175px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f3f3;
  border-radius: 8px;
  overflow: hidden;
}

.home-skeleton-img {
  width: 100%;
  height: 100%;
  background: linear-gradient(-90deg, #f0f0f0 0%, #e0e0e0 50%, #f0f0f0 100%);
  background-size: 200% 100%;
  animation: home-loading 1.2s ease-in-out infinite;
}

@keyframes home-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.home-animate-pulse {
  animation: home-loading 1.5s infinite linear;
}

/* Swiper Styles - Following Original Structure */
.home-banner-container.swiper,
.home-banner-item {
  position: relative;
}

.home-swiper-pagination {
  position: absolute;
  bottom: 20px !important;
  z-index: 10;
}

.home-swiper-pagination .swiper-pagination-bullet-active {
  background-color: #e6b120 !important;
}

/* Section Styles */
.home-section-wrapper {
  margin-top: 2rem;
}

.home-section-title {
  font-family: 'Istok Web', sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 1rem;
  color: #313131;
  margin-bottom: 1rem;
}

.home-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.home-view-all {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.home-view-all:hover {
  opacity: 0.8;
}

.home-view-all-text {
  font-size: 12px;
  text-transform: capitalize;
  color: #313131;
  margin-right: 0.25rem;
}

.home-view-all-arrow {
  font-size: 14px;
  color: #313131;
}

.home-categories-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.home-categories-container {
  position: relative;
}

.home-categories-swiper {
  padding-bottom: 2rem;
}

.home-categories-pagination {
  display: none;
  position: absolute;
  bottom: 0 !important;
  left: 50% !important;
  transform: translateX(-50%);
  width: auto !important;
  text-align: center;
}

.home-categories-pagination .swiper-pagination-bullet {
  background: #e6b120;
  opacity: 0.3;
  width: 8px;
  height: 8px;
  margin: 0 4px;
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.home-categories-pagination .swiper-pagination-bullet-active {
  opacity: 1;
  background: #e6b120;
  transform: scale(1.2);
}

.home-categories-pagination .swiper-pagination-bullet:hover {
  opacity: 0.7;
  transform: scale(1.1);
}

.home-category-card {
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  text-align: center;
}

.home-category-img-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 6px;
  margin-bottom: 8px;
}

.home-category-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.home-category-name {
  font-family: 'Istok Web', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #313131;
  line-height: 1.2;
  margin: 0;
}

/* Product Card */
.home-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 1rem;
}

.home-product-card {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
}

.home-product-img-wrapper {
  height: 150px;
  overflow: hidden;
}

.home-product-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.home-product-info {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.home-product-title {
  font-family: 'Istok Web', sans-serif;
  font-size: 14px;
  text-transform: capitalize;
  color: #313131;
  margin: 0;
  line-height: 1.3;
}

.home-product-price {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 8px 0;
}

.home-product-rating {
  display: flex;
  align-items: center;
  font-size: 11px;
  color: #666;
  gap: 4px;
}

.home-rating-stars {
  color: #FFD700;
}

.home-rating-divider {
  color: #ddd;
}

.home-sales-count {
  color: #666;
}

.home-product-meta {
  font-size: 10px;
  color: #838383;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin: 0;
}

/* Loading and Error States */
.home-loading-text {
  text-align: center;
  color: #838383;
  font-size: 14px;
  padding: 2rem;
  margin: 0;
}

.home-no-data {
  text-align: center;
  color: #838383;
  font-size: 14px;
  padding: 2rem;
  margin: 0;
}

.home-error-text {
  text-align: center;
  color: #dc2626;
  font-size: 14px;
  padding: 2rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 639px) {
  .home-categories-container {
    width: 100%;
  }

  .home-categories-swiper {
    padding-bottom: 2rem;
  }

  .home-categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .home-category-card {
    width: 100px;
    height: auto;
    margin: 0 auto;
  }

  .home-category-img-wrapper {
    width: 100px;
    height: 100px;
  }

  .home-category-name {
    font-size: 11px;
    text-align: center;
  }
}

@media (min-width: 400px) {
  .home-categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .home-categories-pagination {
    display: block;
  }

  .home-category-card {
    width: 80px;
    height: auto;
    margin: 0 auto;
  }

  .home-category-img-wrapper {
    width: 80px;
    height: 80px;
  }

  .home-category-name {
    font-size: 11px;
    text-align: center;
  }
}

@media (min-width: 640px) {
  .home-main-layout {
    margin-top: 1.25rem;
    max-width: 700px;
  }

  .home-banner-item {
    height: 200px;
  }

  .home-banner-skeleton {
    height: 200px;
  }

  .home-categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .home-category-img-wrapper {
    height: 70px;
  }

  .home-category-name {
    font-size: 13px;
  }

  .home-product-img-wrapper {
    height: 140px;
  }

  .home-product-name {
    font-family: 'Istok Web', sans-serif;
    font-weight: 400;
    font-size: 14px;
  }

  .home-product-price {
    font-size: 15px;
  }
}

@media (min-width: 768px) {
  .home-main-layout {
    margin-top: 2rem;
    padding-left: 0;
    padding-right: 0;
  }

  .home-category-card {
    width: 120px;
  }
  
  .home-categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .home-section-title {
    font-size: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .home-category-card {
    width: 100px;
  }

  .home-categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .home-products-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }

  .home-product-card {
    width: 200px;
  }

  .home-product-img-wrapper {
    border-radius: 8px;
    height: 200px;
    width: 200px;
  }

  .home-product-content {
    padding: 12px 5px 0 5px;
  }

  .home-product-name {
    font-family: 'Istok Web', sans-serif;
    font-weight: 400;
    font-size: 15px;
  }

  .home-product-price {
    font-size: 16px;
  }
}

@media (min-width: 1280px) {
  .home-main-layout {
    max-width: 1140px;
  }
  
  .home-banner-item {
    height: 350px;
  }
  
  .home-banner-skeleton {
    height: 350px;
  }
  
  .home-categories-grid {
    grid-template-columns: repeat(6, 1fr);
  }

  .home-category-card {
    width: 150px;
  }
  
  .home-category-img-wrapper {
    height: 150px;
    width: 150px;
  }
  
  .home-category-name {
    font-size: 14px;
  }
  
  .home-products-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}